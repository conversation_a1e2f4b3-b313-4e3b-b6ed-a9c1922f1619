import localForage from "./index";
import crypto from "@/utils/crypto";
import Cookies from "js-cookie";

/**
 * 存储类型枚举
 */
const STORAGE_TYPES = {
  LOCALFORAGE: "localForage",
  LOCAL: "local",
  SESSION: "session",
  COOKIE: "cookie",
};

/**
 * 默认配置
 */
const DEFAULT_CONFIG = {
  // 默认过期时间（秒），0 表示永不过期
  defaultExpire: 0,
  // 是否启用加密
  enableEncryption: true,
  // 是否启用压缩（对于大数据）
  enableCompression: false,
  // 数据版本号，用于数据迁移
  dataVersion: "1.0.0",
  // Cookie 默认配置
  cookieOptions: {
    // Cookie 路径
    path: "/",
    // Cookie 域名（默认为当前域名）
    domain: undefined,
    // 是否仅 HTTPS
    secure: false,
    // SameSite 策略
    sameSite: "Lax",
  },
};

/**
 * 性能优化的缓存管理器
 */
class OptimizedCache {
  constructor() {
    this.keyValidationCache = new Map();
    this.dataStructureCache = new Map();
    this.sizeCache = new Map();
    this.lastCleanup = Date.now();
    this.cleanupInterval = 300000; // 5分钟清理一次缓存
  }

  // 缓存键验证结果
  isValidKey(key) {
    if (this.keyValidationCache.has(key)) {
      return this.keyValidationCache.get(key);
    }
    const isValid = typeof key === "string" && key.length > 0;
    this.keyValidationCache.set(key, isValid);
    return isValid;
  }

  // 缓存数据结构验证
  isValidStoreData(data) {
    const cacheKey = JSON.stringify(Object.keys(data || {}));
    if (this.dataStructureCache.has(cacheKey)) {
      return this.dataStructureCache.get(cacheKey);
    }
    const isValid = data && typeof data === "object" && data.hasOwnProperty("data") && data.hasOwnProperty("expire") && data.hasOwnProperty("timestamp");
    this.dataStructureCache.set(cacheKey, isValid);
    return isValid;
  }

  // 定期清理缓存
  cleanup() {
    const now = Date.now();
    if (now - this.lastCleanup > this.cleanupInterval) {
      this.keyValidationCache.clear();
      this.dataStructureCache.clear();
      this.sizeCache.clear();
      this.lastCleanup = now;
    }
  }
}

/**
 * 增强版本地存储类（性能优化版）
 * 支持过期时间、加密、压缩、多存储类型等功能
 */
class EnhancedStore {
  constructor(config = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.db = localForage;

    // 性能监控
    this.metrics = {
      hits: 0,
      misses: 0,
      errors: 0,
    };

    // 优化缓存管理器
    this.cache = new OptimizedCache();

    // 预编译正则表达式和常量
    this.timestampNow = Date.now;
    this.compressionThreshold = 1024;
    this.cookieSizeLimit = 4096;

    // 批量操作缓冲区
    this.batchBuffer = {
      operations: [],
      timer: null,
      batchSize: 10,
      delay: 50,
    };
  }

  /**
   * 存储数据（优化版）
   * @param {string} key - 存储键
   * @param {any} value - 存储值
   * @param {Object} options - 配置选项
   * @returns {Promise<boolean>} 是否存储成功
   */
  async setItem(key, value, options = {}) {
    try {
      // 优化的参数验证
      if (!this.cache.isValidKey(key)) {
        throw new Error("无效的存储键：键必须是非空字符串");
      }

      // 解构选项（减少对象访问）
      const type = options.type || STORAGE_TYPES.LOCALFORAGE;
      const expire = options.expire !== undefined ? options.expire : this.config.defaultExpire;
      const encrypt = options.encrypt !== undefined ? options.encrypt : this.config.enableEncryption;
      const compress = options.compress !== undefined ? options.compress : this.config.enableCompression;
      const cookieOptions = options.cookieOptions || {};

      // 优化的数据结构构建
      const storeData = this._buildStoreDataOptimized(value, expire, encrypt, compress);

      // 根据存储类型进行存储
      const success = await this._performStoreOptimized(key, storeData, type, { expire, cookieOptions });

      if (success && process.env.NODE_ENV === "development") {
        console.debug(`✅ 数据存储成功: ${key} (${type})`);
      }

      return success;
    } catch (error) {
      this.metrics.errors++;
      if (process.env.NODE_ENV === "development") {
        console.error(`❌ 存储数据失败，键: ${key}`, error);
      }
      return false;
    }
  }

  /**
   * 获取数据（优化版）
   * @param {string} key - 存储键
   * @param {Object} options - 配置选项
   * @returns {Promise<any>} 存储的数据或默认值
   */
  async getItem(key, options = {}) {
    try {
      // 优化的参数验证
      if (!this.cache.isValidKey(key)) {
        throw new Error("无效的存储键：键必须是非空字符串");
      }

      const { defaultValue = null, autoCleanExpired = true } = options;

      // 尝试从不同存储中获取数据（优化版）
      const rawData = await this._retrieveDataOptimized(key);

      if (!rawData) {
        this.metrics.misses++;
        return defaultValue;
      }

      // 解析和验证数据（优化版）
      const parsedData = this._parseStoreDataOptimized(rawData);

      if (!parsedData) {
        this.metrics.misses++;
        return defaultValue;
      }

      // 优化的过期检查
      if (this._isExpiredOptimized(parsedData)) {
        if (autoCleanExpired) {
          // 异步清理，不阻塞返回
          this._asyncRemoveItem(key);
        }
        this.metrics.misses++;
        return defaultValue;
      }

      this.metrics.hits++;
      return parsedData.data;
    } catch (error) {
      this.metrics.errors++;
      if (process.env.NODE_ENV === "development") {
        console.error(`❌ 获取数据失败，键: ${key}`, error);
      }
      return options.defaultValue || null;
    }
  }

  /**
   * 删除数据（优化版）
   * @param {string} key - 存储键
   * @returns {Promise<boolean>} 是否删除成功
   */
  async removeItem(key) {
    try {
      if (!this.cache.isValidKey(key)) {
        return false;
      }

      // 并行删除所有存储类型（优化版）
      const promises = [
        this.db.removeItem(key).catch(() => false),
        this._removeFromStorageOptimized(localStorage, key),
        this._removeFromStorageOptimized(sessionStorage, key),
        this._removeCookieOptimized(key),
      ];

      await Promise.allSettled(promises); // 使用 allSettled 避免一个失败影响其他

      if (process.env.NODE_ENV === "development") {
        console.debug(`🗑️ 数据已删除: ${key}`);
      }
      return true;
    } catch (error) {
      this.metrics.errors++;
      if (process.env.NODE_ENV === "development") {
        console.error(`❌ 删除数据失败，键: ${key}`, error);
      }
      return false;
    }
  }

  /**
   * 异步删除（非阻塞）
   * @private
   */
  _asyncRemoveItem(key) {
    // 使用 setTimeout 确保异步执行，不阻塞当前操作
    setTimeout(() => {
      this.removeItem(key).catch(() => {});
    }, 0);
  }

  /**
   * 清空所有数据（优化版）
   * @returns {Promise<boolean>} 是否清空成功
   */
  async clear() {
    try {
      // 并行清空所有存储类型
      const promises = [this.db.clear().catch(() => false), this._clearStorageOptimized(localStorage), this._clearStorageOptimized(sessionStorage), this._clearAllCookiesOptimized()];

      await Promise.allSettled(promises);

      if (process.env.NODE_ENV === "development") {
        console.debug("🧹 所有数据已清空");
      }
      return true;
    } catch (error) {
      this.metrics.errors++;
      if (process.env.NODE_ENV === "development") {
        console.error("❌ 清空数据失败", error);
      }
      return false;
    }
  }

  /**
   * 获取所有键（优化版）
   * @param {string} type - 存储类型
   * @returns {Promise<string[]>} 键数组
   */
  async keys(type = STORAGE_TYPES.LOCALFORAGE) {
    try {
      switch (type) {
        case STORAGE_TYPES.LOCALFORAGE:
          return await this.db.keys();
        case STORAGE_TYPES.LOCAL:
          return Object.keys(localStorage);
        case STORAGE_TYPES.SESSION:
          return Object.keys(sessionStorage);
        case STORAGE_TYPES.COOKIE:
          return this._getCookieKeysOptimized();
        default:
          return [];
      }
    } catch (error) {
      if (process.env.NODE_ENV === "development") {
        console.error(`❌ 获取键列表失败，存储类型: ${type}`, error);
      }
      return [];
    }
  }

  /**
   * 获取存储大小信息（优化版）
   * @returns {Promise<Object>} 存储大小信息
   */
  async getStorageInfo() {
    try {
      // 定期清理缓存
      this.cache.cleanup();

      // 并行获取所有存储类型的信息
      const [localForageInfo, localStorageInfo, sessionStorageInfo, cookieInfo] = await Promise.allSettled([
        this._getStorageSizeOptimized(STORAGE_TYPES.LOCALFORAGE),
        this._getStorageSizeOptimized(STORAGE_TYPES.LOCAL),
        this._getStorageSizeOptimized(STORAGE_TYPES.SESSION),
        this._getStorageSizeOptimized(STORAGE_TYPES.COOKIE),
      ]);

      return {
        localForage: localForageInfo.status === "fulfilled" ? localForageInfo.value : { type: STORAGE_TYPES.LOCALFORAGE, itemCount: 0, totalSize: 0, formattedSize: "0 B" },
        localStorage: localStorageInfo.status === "fulfilled" ? localStorageInfo.value : { type: STORAGE_TYPES.LOCAL, itemCount: 0, totalSize: 0, formattedSize: "0 B" },
        sessionStorage: sessionStorageInfo.status === "fulfilled" ? sessionStorageInfo.value : { type: STORAGE_TYPES.SESSION, itemCount: 0, totalSize: 0, formattedSize: "0 B" },
        cookie: cookieInfo.status === "fulfilled" ? cookieInfo.value : { type: STORAGE_TYPES.COOKIE, itemCount: 0, totalSize: 0, formattedSize: "0 B" },
        metrics: { ...this.metrics },
      };
    } catch (error) {
      if (process.env.NODE_ENV === "development") {
        console.error("❌ 获取存储信息失败", error);
      }
      return null;
    }
  }

  /**
   * 清理过期数据（优化版）
   * @returns {Promise<number>} 清理的数据条数
   */
  async cleanExpired() {
    let cleanedCount = 0;

    try {
      // 并行清理所有存储类型的过期数据
      const [localForageCleaned, localStorageCleaned, sessionStorageCleaned, cookieCleaned] = await Promise.allSettled([
        this._cleanExpiredFromLocalForageOptimized(),
        this._cleanExpiredFromStorageOptimized(localStorage),
        this._cleanExpiredFromStorageOptimized(sessionStorage),
        this._cleanExpiredFromCookiesOptimized(),
      ]);

      cleanedCount += localForageCleaned.status === "fulfilled" ? localForageCleaned.value : 0;
      cleanedCount += localStorageCleaned.status === "fulfilled" ? localStorageCleaned.value : 0;
      cleanedCount += sessionStorageCleaned.status === "fulfilled" ? sessionStorageCleaned.value : 0;
      cleanedCount += cookieCleaned.status === "fulfilled" ? cookieCleaned.value : 0;

      if (process.env.NODE_ENV === "development") {
        console.debug(`🧹 已清理 ${cleanedCount} 条过期数据`);
      }
      return cleanedCount;
    } catch (error) {
      if (process.env.NODE_ENV === "development") {
        console.error("❌ 清理过期数据失败", error);
      }
      return 0;
    }
  }

  /**
   * 批量设置数据（优化版）
   * @param {Object} items - 键值对对象
   * @param {Object} options - 配置选项
   * @returns {Promise<boolean>} 是否全部设置成功
   */
  async setItems(items, options = {}) {
    try {
      const entries = Object.entries(items);

      // 使用批处理优化
      if (entries.length > this.batchBuffer.batchSize) {
        return await this._batchSetItems(entries, options);
      }

      const promises = entries.map(([key, value]) => this.setItem(key, value, options));
      const results = await Promise.allSettled(promises);

      return results.every((result) => result.status === "fulfilled" && result.value === true);
    } catch (error) {
      if (process.env.NODE_ENV === "development") {
        console.error("❌ 批量设置数据失败", error);
      }
      return false;
    }
  }

  /**
   * 批量获取数据（优化版）
   * @param {string[]} keys - 键数组
   * @param {Object} options - 配置选项
   * @returns {Promise<Object>} 键值对对象
   */
  async getItems(keys, options = {}) {
    try {
      // 使用批处理优化
      if (keys.length > this.batchBuffer.batchSize) {
        return await this._batchGetItems(keys, options);
      }

      const promises = keys.map(async (key) => {
        const value = await this.getItem(key, options);
        return [key, value];
      });

      const results = await Promise.allSettled(promises);
      const successResults = results.filter((result) => result.status === "fulfilled").map((result) => result.value);

      return Object.fromEntries(successResults);
    } catch (error) {
      if (process.env.NODE_ENV === "development") {
        console.error("❌ 批量获取数据失败", error);
      }
      return {};
    }
  }

  // ==================== 优化的私有方法 ====================

  /**
   * 优化的数据结构构建
   * @private
   */
  _buildStoreDataOptimized(value, expire, encrypt, compress) {
    const now = this.timestampNow();
    const expireTime = expire > 0 ? now + expire * 1000 : 0;

    // 减少对象创建，直接构建最终结构
    const baseData = {
      data: value,
      expire: expireTime,
      timestamp: now,
      version: this.config.dataVersion,
      compressed: false,
      encrypted: false,
    };

    // 压缩处理（如果启用）
    if (compress && this._shouldCompressOptimized(value)) {
      baseData.data = this._compressOptimized(baseData.data);
      baseData.compressed = true;
    }

    // 一次性序列化
    const serializedData = JSON.stringify(baseData);

    // 加密处理
    if (encrypt) {
      return this._encryptOptimized(serializedData);
    }

    return serializedData;
  }

  /**
   * 优化的存储执行
   * @private
   */
  async _performStoreOptimized(key, data, type, extraOptions = {}) {
    switch (type) {
      case STORAGE_TYPES.LOCALFORAGE:
        await this.db.setItem(key, data);
        return true;
      case STORAGE_TYPES.LOCAL:
        localStorage.setItem(key, data);
        return true;
      case STORAGE_TYPES.SESSION:
        sessionStorage.setItem(key, data);
        return true;
      case STORAGE_TYPES.COOKIE:
        return this._setCookieOptimized(key, data, extraOptions);
      default:
        throw new Error(`不支持的存储类型: ${type}`);
    }
  }

  /**
   * 优化的Cookie设置
   * @private
   */
  _setCookieOptimized(key, data, { expire, cookieOptions }) {
    try {
      // 预先计算数据大小
      const dataSize = new Blob([data]).size;
      if (dataSize > this.cookieSizeLimit) {
        if (process.env.NODE_ENV === "development") {
          console.warn(`⚠️ Cookie 数据大小 (${dataSize} 字节) 超过 4KB 限制，键: ${key}`);
        }
      }

      // 合并选项（减少对象创建）
      const options = Object.assign({}, this.config.cookieOptions, cookieOptions);

      // 设置过期时间
      if (expire > 0) {
        options.expires = new Date(Date.now() + expire * 1000);
      }

      Cookies.set(key, data, options);
      return true;
    } catch (error) {
      if (process.env.NODE_ENV === "development") {
        console.error(`❌ 设置 Cookie 失败，键: ${key}`, error);
      }
      return false;
    }
  }

  /**
   * 优化的数据检索
   * @private
   */
  async _retrieveDataOptimized(key) {
    // 创建获取函数数组，避免重复代码
    const retrievers = [() => this.db.getItem(key), () => Promise.resolve(localStorage.getItem(key)), () => Promise.resolve(sessionStorage.getItem(key)), () => Promise.resolve(Cookies.get(key))];

    // 按优先级串行尝试（保持原有逻辑，因为串行在这里是有意义的）
    for (const retriever of retrievers) {
      try {
        const value = await retriever();
        if (value !== null && value !== undefined) return value;
      } catch (_error) {
        // 静默继续尝试下一个存储
        continue;
      }
    }

    return null;
  }

  /**
   * 优化的数据解析
   * @private
   */
  _parseStoreDataOptimized(rawData) {
    try {
      // 尝试解密
      let decryptedData;
      try {
        decryptedData = this._decryptOptimized(rawData);
      } catch {
        decryptedData = rawData;
      }

      const parsedData = JSON.parse(decryptedData);

      // 优化的数据结构验证
      if (!this.cache.isValidStoreData(parsedData)) {
        if (process.env.NODE_ENV === "development") {
          console.warn("无效的存储数据结构");
        }
        return null;
      }

      // 解压缩处理
      if (parsedData.compressed) {
        parsedData.data = this._decompressOptimized(parsedData.data);
        parsedData.compressed = false;
      }

      return parsedData;
    } catch (error) {
      if (process.env.NODE_ENV === "development") {
        console.warn("解析存储数据失败", error);
      }
      return null;
    }
  }

  /**
   * 优化的过期检查
   * @private
   */
  _isExpiredOptimized(storeData) {
    return storeData.expire > 0 && Date.now() > storeData.expire;
  }

  /**
   * 优化的加密
   * @private
   */
  _encryptOptimized(data) {
    return crypto.encrypt(data);
  }

  /**
   * 优化的解密
   * @private
   */
  _decryptOptimized(data) {
    return crypto.decrypt(data);
  }

  /**
   * 优化的压缩判断
   * @private
   */
  _shouldCompressOptimized(data) {
    // 预先计算字符串长度，避免重复序列化
    if (typeof data === "string") {
      return data.length > this.compressionThreshold;
    }
    return JSON.stringify(data).length > this.compressionThreshold;
  }

  /**
   * 优化的压缩
   * @private
   */
  _compressOptimized(data) {
    const str = typeof data === "string" ? data : JSON.stringify(data);
    return btoa(encodeURIComponent(str));
  }

  /**
   * 优化的解压缩
   * @private
   */
  _decompressOptimized(compressedData) {
    try {
      const str = decodeURIComponent(atob(compressedData));
      try {
        return JSON.parse(str);
      } catch {
        return str;
      }
    } catch (error) {
      if (process.env.NODE_ENV === "development") {
        console.error("解压缩失败", error);
      }
      return compressedData;
    }
  }

  /**
   * 优化的存储删除
   * @private
   */
  _removeFromStorageOptimized(storage, key) {
    try {
      storage.removeItem(key);
      return Promise.resolve(true);
    } catch (_error) {
      return Promise.resolve(false);
    }
  }

  /**
   * 优化的Cookie删除
   * @private
   */
  _removeCookieOptimized(key) {
    try {
      Cookies.remove(key, this.config.cookieOptions);
      return Promise.resolve(true);
    } catch (_error) {
      return Promise.resolve(false);
    }
  }

  /**
   * 优化的存储清理
   * @private
   */
  _clearStorageOptimized(storage) {
    try {
      storage.clear();
      return Promise.resolve(true);
    } catch (_error) {
      return Promise.resolve(false);
    }
  }

  /**
   * 优化的Cookie全清理
   * @private
   */
  _clearAllCookiesOptimized() {
    try {
      const cookieKeys = this._getCookieKeysOptimized();
      cookieKeys.forEach((key) => {
        Cookies.remove(key, this.config.cookieOptions);
      });
      return Promise.resolve(true);
    } catch (_error) {
      return Promise.resolve(false);
    }
  }

  /**
   * 优化的Cookie键获取
   * @private
   */
  _getCookieKeysOptimized() {
    try {
      // 缓存结果以避免重复解析
      const cacheKey = document.cookie;
      if (this.cache.sizeCache.has(cacheKey)) {
        return this.cache.sizeCache.get(cacheKey);
      }

      const keys = document.cookie
        .split(";")
        .map((cookie) => cookie.trim().split("=")[0])
        .filter((key) => key.length > 0);

      this.cache.sizeCache.set(cacheKey, keys);
      return keys;
    } catch (_error) {
      return [];
    }
  }

  /**
   * 优化的存储大小计算
   * @private
   */
  async _getStorageSizeOptimized(type) {
    try {
      let totalSize = 0;
      let itemCount = 0;
      // 预留缓存变量用于未来优化
      // const _blobCache = new Map();

      switch (type) {
        case STORAGE_TYPES.LOCALFORAGE:
          const keys = await this.db.keys();
          itemCount = keys.length;
          // 批量获取以提高性能
          const values = await Promise.allSettled(keys.map((key) => this.db.getItem(key)));
          values.forEach((result) => {
            if (result.status === "fulfilled" && result.value) {
              const serialized = JSON.stringify(result.value);
              totalSize += new Blob([serialized]).size;
            }
          });
          break;

        case STORAGE_TYPES.LOCAL:
        case STORAGE_TYPES.SESSION:
          const storage = type === STORAGE_TYPES.LOCAL ? localStorage : sessionStorage;
          itemCount = storage.length;
          // 批量计算大小
          for (let i = 0; i < storage.length; i++) {
            const key = storage.key(i);
            const value = storage.getItem(key);
            if (value) {
              totalSize += new Blob([value]).size;
            }
          }
          break;

        case STORAGE_TYPES.COOKIE:
          const cookieKeys = this._getCookieKeysOptimized();
          itemCount = cookieKeys.length;
          cookieKeys.forEach((key) => {
            const value = Cookies.get(key);
            if (value) {
              totalSize += new Blob([value]).size;
            }
          });
          break;
      }

      return {
        type,
        itemCount,
        totalSize,
        formattedSize: this._formatBytesOptimized(totalSize),
      };
    } catch (error) {
      if (process.env.NODE_ENV === "development") {
        console.error(`获取存储大小失败，存储类型: ${type}`, error);
      }
      return { type, itemCount: 0, totalSize: 0, formattedSize: "0 B" };
    }
  }

  /**
   * 优化的字节格式化
   * @private
   */
  _formatBytesOptimized(bytes) {
    if (bytes === 0) return "0 B";

    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return (bytes / Math.pow(k, i)).toFixed(2) + " " + sizes[i];
  }

  /**
   * 优化的localForage过期清理
   * @private
   */
  async _cleanExpiredFromLocalForageOptimized() {
    let cleanedCount = 0;
    try {
      const keys = await this.db.keys();
      const expiredKeys = [];

      // 批量检查过期状态
      const checkPromises = keys.map(async (key) => {
        try {
          const data = await this.getItem(key, { autoCleanExpired: false });
          if (data === null) {
            expiredKeys.push(key);
          }
        } catch {
          // 忽略错误，继续处理其他键
        }
      });

      await Promise.allSettled(checkPromises);

      // 批量删除过期数据
      const removePromises = expiredKeys.map((key) => this.db.removeItem(key).catch(() => false));
      await Promise.allSettled(removePromises);

      cleanedCount = expiredKeys.length;
    } catch (error) {
      if (process.env.NODE_ENV === "development") {
        console.error("清理 localForage 过期数据失败", error);
      }
    }
    return cleanedCount;
  }

  /**
   * 优化的存储过期清理
   * @private
   */
  async _cleanExpiredFromStorageOptimized(storage) {
    let cleanedCount = 0;
    const keysToRemove = [];

    try {
      // 收集需要删除的键
      for (let i = 0; i < storage.length; i++) {
        const key = storage.key(i);
        if (!key) continue;

        const rawData = storage.getItem(key);
        if (!rawData) continue;

        const parsedData = this._parseStoreDataOptimized(rawData);
        if (parsedData && this._isExpiredOptimized(parsedData)) {
          keysToRemove.push(key);
        }
      }

      // 批量删除
      keysToRemove.forEach((key) => {
        try {
          storage.removeItem(key);
          cleanedCount++;
        } catch {
          // 忽略单个删除错误
        }
      });
    } catch (error) {
      if (process.env.NODE_ENV === "development") {
        console.error("清理存储过期数据失败", error);
      }
    }

    return cleanedCount;
  }

  /**
   * 优化的Cookie过期清理
   * @private
   */
  async _cleanExpiredFromCookiesOptimized() {
    let cleanedCount = 0;

    try {
      const cookieKeys = this._getCookieKeysOptimized();

      // 批量检查和清理
      const cleanupPromises = cookieKeys.map(async (key) => {
        try {
          const rawData = Cookies.get(key);
          if (!rawData) return false;

          const parsedData = this._parseStoreDataOptimized(rawData);
          if (parsedData && this._isExpiredOptimized(parsedData)) {
            this._removeCookieOptimized(key);
            return true;
          }
        } catch {
          return false;
        }
        return false;
      });

      const results = await Promise.allSettled(cleanupPromises);
      cleanedCount = results.filter((result) => result.status === "fulfilled" && result.value === true).length;
    } catch (error) {
      if (process.env.NODE_ENV === "development") {
        console.error("清理 Cookie 过期数据失败", error);
      }
    }

    return cleanedCount;
  }

  /**
   * 批量设置数据优化
   * @private
   */
  async _batchSetItems(entries, options) {
    const chunkSize = this.batchBuffer.batchSize;
    const chunks = [];

    for (let i = 0; i < entries.length; i += chunkSize) {
      chunks.push(entries.slice(i, i + chunkSize));
    }

    const results = [];

    for (const chunk of chunks) {
      const chunkPromises = chunk.map(([key, value]) => this.setItem(key, value, options));
      const chunkResults = await Promise.allSettled(chunkPromises);
      results.push(...chunkResults);

      // 短暂延迟以避免阻塞UI
      if (chunk !== chunks[chunks.length - 1]) {
        await new Promise((resolve) => setTimeout(resolve, this.batchBuffer.delay));
      }
    }

    return results.every((result) => result.status === "fulfilled" && result.value === true);
  }

  /**
   * 批量获取数据优化
   * @private
   */
  async _batchGetItems(keys, options) {
    const chunkSize = this.batchBuffer.batchSize;
    const chunks = [];

    for (let i = 0; i < keys.length; i += chunkSize) {
      chunks.push(keys.slice(i, i + chunkSize));
    }

    const allResults = [];

    for (const chunk of chunks) {
      const chunkPromises = chunk.map(async (key) => {
        const value = await this.getItem(key, options);
        return [key, value];
      });

      const chunkResults = await Promise.allSettled(chunkPromises);
      const successResults = chunkResults.filter((result) => result.status === "fulfilled").map((result) => result.value);

      allResults.push(...successResults);

      // 短暂延迟以避免阻塞UI
      if (chunk !== chunks[chunks.length - 1]) {
        await new Promise((resolve) => setTimeout(resolve, this.batchBuffer.delay));
      }
    }

    return Object.fromEntries(allResults);
  }

  // ==================== 兼容性方法 ====================

  /**
   * 兼容旧版本的加密方法
   * @deprecated 使用 _encryptOptimized 替代
   */
  encrypt(value) {
    if (process.env.NODE_ENV === "development") {
      console.warn("encrypt 方法已废弃，请使用 _encryptOptimized 替代");
    }
    return this._encryptOptimized(JSON.stringify(value));
  }

  /**
   * 兼容旧版本的解密方法
   * @deprecated 使用 _decryptOptimized 替代
   */
  decrypt(value) {
    if (process.env.NODE_ENV === "development") {
      console.warn("decrypt 方法已废弃，请使用 _decryptOptimized 替代");
    }
    try {
      const decrypted = this._decryptOptimized(value);
      return JSON.parse(decrypted);
    } catch (error) {
      if (process.env.NODE_ENV === "development") {
        console.error("解密和解析数据失败", error);
      }
      return null;
    }
  }
}

// 创建单例实例
const enhancedStore = new EnhancedStore();

// 导出增强的存储实例和类
export { EnhancedStore, STORAGE_TYPES };
export default enhancedStore;
